import { Player } from '@/entities/Player.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { Loaded, PopulatePath } from '@mikro-orm/core';

export type Auction = Loaded<
  TransferListedPlayer,
  'player' | 'player.team' | 'player.team.manager' | 'bidHistory' | 'bidHistory.team',
  PopulatePath.ALL,
  never
>;

export type DBTransferListedPlayer = Loaded<
  TransferListedPlayer,
  'player' | 'player.attributes' | 'bidHistory' | 'bidHistory.team',
  PopulatePath.ALL,
  never
>;

export type DBTransferRequest = Loaded<
  TransferRequest,
  'player' | 'buyer' | 'seller' | 'seller.players.value',
  PopulatePath.ALL,
  never
>;

/**
 * Repository for transfer requests
 */
export interface TransferRepository {
  /**
   * Get all transfer listed players for a gameworld
   * @param gameworldId The gameworld ID
   * @param limit Optional limit for pagination
   * @param lastEvaluatedKey Optional key for pagination
   * @returns Array of transfer listed players
   */
  getTransferListedPlayers(
    gameworldId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<DBTransferListedPlayer[]>;

  /**
   * Submit a transfer offer for a player
   * @param playerId The ID of the player to transfer
   * @param value The offer value
   * @param buyerTeamId The ID of the buying team
   * @param sellerTeamId The ID of the selling team
   * @returns The created transfer request
   */
  submitOffer(
    playerId: string,
    value: number,
    buyerTeamId: string,
    sellerTeamId: string
  ): Promise<TransferRequest>;

  /**
   * Get a transfer request by ID
   * @param transferRequestId The ID of the transfer request
   * @returns The transfer request, or null if not found
   */
  getTransferRequest(transferRequestId: string): Promise<TransferRequest | null>;

  getTransferRequestsToAI(): Promise<DBTransferRequest[]>;

  /**
   * Get all transfer requests for a team as seller
   * @param teamId The ID of the selling team
   * @returns Array of transfer requests
   */
  getTransferRequestsBySeller(teamId: string): Promise<TransferRequest[]>;

  /**
   * Get all transfer requests for a team as buyer
   * @param teamId The ID of the buying team
   * @returns Array of transfer requests
   */
  getTransferRequestsByBuyer(teamId: string): Promise<TransferRequest[]>;

  /**
   * Get all transfer requests for a player
   * @param playerId The ID of the player
   * @returns Array of transfer requests
   */
  getTransferRequestsByPlayer(playerId: string): Promise<TransferRequest[]>;

  /**
   * Update a transfer request with a counter offer
   * @param transferRequest The transfer request
   * @param counterOfferValue The value of the counter offer
   * @returns The updated transfer request
   */
  submitCounterOffer(
    transferRequest: TransferRequest,
    counterOfferValue: number,
    flush?: boolean
  ): Promise<TransferRequest>;

  /**
   * Delete a transfer request
   * @param transferRequestId The ID of the transfer request to delete
   */
  deleteTransferRequest(transferRequestId: string): Promise<void>;

  submitBid(
    player: string,
    maxBid: number,
    myTeam: string
  ): Promise<{ maxBid: number; highestBidder: boolean }>;

  getCompletedAuctions(): Promise<Auction[]>;

  updateTransferListedPlayer(transferListedPlayer: TransferListedPlayer): Promise<void>;

  deleteTransferListedPlayer(auctionId: string): Promise<void>;

  getActiveAuctions(gameworldId: string): Promise<number>;
  addTransferListedPlayer(player: Player): Promise<void>;

  /**
   * Get transfer listed players that a specific team has already bid on
   * @param teamId The ID of the team
   * @param gameworldId The gameworld ID
   * @returns Array of transfer listed players that the team has bid on
   */
  getTransferListedPlayersWithTeamBids(
    teamId: string,
    gameworldId: string
  ): Promise<DBTransferListedPlayer[]>;

  flush(): Promise<void>;
}
