import { BidHistory } from '@/entities/BidHistory.js';
import { Player } from '@/entities/Player.js';
import { Team } from '@/entities/Team.js';
import { TransferListedPlayer } from '@/entities/TransferListedPlayer.js';
import { TransferRequest } from '@/entities/TransferRequest.js';
import { MikroOrmService } from '@/storage-interface/mikro-orm-service.js';
import {
  Auction,
  DBTransferListedPlayer,
  DBTransferRequest,
  TransferRepository,
} from '@/storage-interface/transfers/transfer-repository.interface.js';
import { logger } from '@/utils/logger.js';
import { Reference, UniqueConstraintViolationException } from '@mikro-orm/core';

/**
 * MikroORM implementation of the TransferRepository
 */
export class MikroOrmTransferRepository implements TransferRepository {
  constructor(private mikroOrmService: MikroOrmService) {}

  flush(): Promise<void> {
    const em = this.mikroOrmService.getEntityManager();
    return em.flush();
  }

  /**
   * Submit a transfer offer for a player
   * @param playerId The ID of the player to transfer
   * @param value The offer value
   * @param buyerTeamId The ID of the buying team
   * @param sellerTeamId The ID of the selling team
   * @returns The created transfer request
   */
  async submitOffer(
    playerId: string,
    value: number,
    buyerTeamId: string,
    sellerTeamId: string
  ): Promise<TransferRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Create a new transfer request
      const transferRequest = new TransferRequest();
      transferRequest.date = Date.now();
      transferRequest.value = value;
      transferRequest.player = em.getReference(Player, playerId);
      transferRequest.buyer = em.getReference(Team, buyerTeamId);
      transferRequest.seller = em.getReference(Team, sellerTeamId);

      // Save the transfer request
      await em.persistAndFlush(transferRequest);

      return transferRequest;
    } catch (error) {
      if (error instanceof UniqueConstraintViolationException) {
        logger.warn('Duplicate transfer request detected:', {
          playerId,
          buyerTeamId,
          sellerTeamId,
        });
        throw new Error('DUPLICATE_TRANSFER_REQUEST');
      }
      logger.error('Failed to submit transfer offer:', {
        error,
        playerId,
        buyerTeamId,
        sellerTeamId,
      });
      throw error;
    }
  }

  /**
   * Get a transfer request by ID
   * @param transferRequestId The ID of the transfer request
   * @returns The transfer request, or null if not found
   */
  async getTransferRequest(transferRequestId: string): Promise<TransferRequest | null> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.findOne(
        TransferRequest,
        { id: transferRequestId },
        {
          populate: ['player', 'buyer', 'seller'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer request:', { error, transferRequestId });
      throw error;
    }
  }

  /**
   * Get all transfer requests for sent to AI managers
   * @returns Array of transfer requests
   */
  async getTransferRequestsToAI(): Promise<DBTransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      // First get the IDs with the date comparison
      const qb = em
        .createQueryBuilder(TransferRequest, 'tr')
        .select('tr.id')
        .leftJoin('tr.seller', 's')
        .where('tr.date > tr.counter_offer_time')
        .andWhere('s.manager_id IS NULL');

      const ids = await qb.execute();

      // Then fetch the full entities with populations
      return await em.find(
        TransferRequest,
        {
          id: { $in: ids.map((row) => row.id) },
        },
        {
          populate: [
            'seller',
            'seller.players',
            'buyer',
            'buyer.manager',
            'buyer.manager.email',
            'buyer.manager.notificationPreferences',
            'buyer.manager.pushToken',
            'player',
          ],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by buyer:', { error });
      throw error;
    }
  }

  /**
   * Get all transfer requests for a team as seller
   * @param teamId The ID of the selling team
   * @returns Array of transfer requests
   */
  async getTransferRequestsBySeller(teamId: string): Promise<TransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        TransferRequest,
        { seller: teamId },
        {
          populate: ['player', 'buyer'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by seller:', { error, teamId });
      throw error;
    }
  }

  /**
   * Get all transfer requests for a player
   * @param playerId The ID of the player
   * @returns Array of transfer requests
   */
  async getTransferRequestsByPlayer(playerId: string): Promise<TransferRequest[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.find(
        TransferRequest,
        { player: playerId },
        {
          populate: ['buyer', 'seller'],
        }
      );
    } catch (error) {
      logger.error('Failed to get transfer requests by player:', { error, playerId });
      throw error;
    }
  }

  /**
   * Update a transfer request with a counter offer
   * @param transferRequest The transfer request
   * @param counterOfferValue The value of the counter offer
   * @param flush Should we flush the database
   * @returns The updated transfer request
   */
  async submitCounterOffer(
    transferRequest: TransferRequest,
    counterOfferValue: number,
    flush = true
  ): Promise<TransferRequest> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Update with counter offer details
      transferRequest.counterOfferTime = Date.now();
      transferRequest.counterOfferValue = counterOfferValue;

      // Save the updated transfer request
      if (flush) {
        await em.persistAndFlush(transferRequest);
      } else {
        em.persist(transferRequest);
      }

      return transferRequest;
    } catch (error) {
      logger.error('Failed to submit counter offer:', { error, id: transferRequest.id });
      throw error;
    }
  }

  /**
   * Add a transfer listed player
   * @param player The player to add to the transfer list
   */
  addTransferListedPlayer(player: Player): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      const transferListedPlayer = new TransferListedPlayer();
      transferListedPlayer.player = Reference.createFromPK(Player, player.playerId);
      transferListedPlayer.gameworldId = player.gameworldId;
      transferListedPlayer.auctionStartPrice = player.value;
      transferListedPlayer.auctionCurrentPrice = player.value;
      transferListedPlayer.auctionEndTime = Date.now() + 24 * 60 * 60 * 1000; // 24 hours from now
      em.persist(transferListedPlayer);

      player.isTransferListed = true;
      em.persist(player);

      return em.flush();
    } catch (error) {
      logger.error('Failed to add transfer listed player:', { error, player });
      throw error;
    }
  }

  /**
   * Delete a transfer request
   * @param transferRequestId The ID of the transfer request to delete
   */
  async deleteTransferRequest(transferRequestId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find the transfer request
      const transferRequest = await em.findOne(TransferRequest, { id: transferRequestId });
      if (!transferRequest) {
        throw new Error(`Transfer request not found: ${transferRequestId}`);
      }

      // Delete the transfer request
      await em.removeAndFlush(transferRequest);
    } catch (error) {
      logger.error('Failed to delete transfer request:', { error, transferRequestId });
      throw error;
    }
  }

  private roundDownToPowerOf10(value: number): number {
    if (value <= 0) return 0;
    const magnitude = Math.floor(Math.log10(value));
    return Math.floor(value / Math.pow(10, magnitude)) * Math.pow(10, magnitude);
  }

  async submitBid(
    player: string,
    maxBid: number,
    myTeam: string
  ): Promise<{ maxBid: number; highestBidder: boolean }> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      logger.debug('Submitting bid', { player, maxBid, myTeam });

      // Find the transfer listed player
      const transferListedPlayer = await em.findOne(
        TransferListedPlayer,
        { player: Reference.createFromPK(Player, player) },
        {
          populate: ['bidHistory', 'bidHistory.team'],
        }
      );
      if (!transferListedPlayer) {
        throw new Error(`Transfer listed player not found: ${player}`);
      }

      if (transferListedPlayer.auctionCurrentPrice > maxBid) {
        return Promise.resolve({
          maxBid: transferListedPlayer.auctionCurrentPrice,
          highestBidder: false,
        });
      }

      // Check if the team has already made a bid
      const existingBid = await em.findOne(BidHistory, {
        transferListing: transferListedPlayer.id,
        team: Reference.createFromPK(Team, myTeam),
      });

      if (existingBid) {
        // Update the existing bid
        existingBid.maximumBid = maxBid;
        existingBid.bidTime = Date.now();
        em.persist(existingBid);
      } else {
        // Create a new bid
        const newBid = new BidHistory();
        newBid.transferListing = em.getReference(TransferListedPlayer, transferListedPlayer.id);
        newBid.team = em.getReference(Team, myTeam);
        newBid.maximumBid = maxBid;
        newBid.bidTime = Date.now();
        em.persist(newBid);
      }

      // Update the current price of the transfer-listed player
      const bids = await em.find(
        BidHistory,
        { transferListing: transferListedPlayer.id },
        {
          orderBy: { maximumBid: 'ASC' },
        }
      );

      transferListedPlayer.auctionCurrentPrice =
        bids.length > 0
          ? bids[bids.length - 1]!.maximumBid +
            this.roundDownToPowerOf10(transferListedPlayer.auctionCurrentPrice) * 0.1
          : transferListedPlayer.auctionStartPrice;

      // Save the updated transfer-listed player
      await em.persistAndFlush(transferListedPlayer);

      return Promise.resolve({ maxBid, highestBidder: true });
    } catch (error) {
      logger.error('Failed to submit bid:', { error, player, maxBid, myTeam });
      throw error;
    }
  }

  async getCompletedAuctions(): Promise<Auction[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find expired auctions
      const players = await em.find(
        TransferListedPlayer,
        {
          auctionEndTime: { $lt: Date.now() },
        },
        {
          populate: [
            'player',
            'player.team',
            'player.team.manager',
            'bidHistory',
            'bidHistory.team',
          ],
        }
      );

      return Promise.resolve(players);
    } catch (error) {
      logger.error('Failed to get completed auctions:', { error });
      throw error;
    }
  }

  async deleteTransferListedPlayer(auctionId: string): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // First find the entity with its relationships
      const transferListedPlayer = await em.findOne(
        TransferListedPlayer,
        { id: auctionId },
        { populate: ['bidHistory', 'player'] }
      );

      if (transferListedPlayer) {
        // Update the player's transfer listed status
        const player = transferListedPlayer.player.unwrap();
        if (player) {
          player.isTransferListed = false;
          em.persist(player);
        }

        // Remove only the transfer listed player entity
        // This will cascade delete the bid history due to orphanRemoval: true
        await em.removeAndFlush(transferListedPlayer);

        logger.info('Removed transfer listed player and bid history', { auctionId });
      } else {
        logger.warn('Transfer listed player not found for deletion:', { auctionId });
      }
    } catch (error) {
      logger.error('Failed to delete transfer listed player:', { error, auctionId });
      throw error;
    }
  }

  async updateTransferListedPlayer(transferListedPlayer: TransferListedPlayer): Promise<void> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      await em.upsert(TransferListedPlayer, transferListedPlayer);
      return em.flush();
    } catch (error) {
      logger.error('Failed to update transfer listed player:', { error, transferListedPlayer });
      throw error;
    }
  }

  async getActiveAuctions(gameworldId: string): Promise<number> {
    try {
      const em = this.mikroOrmService.getEntityManager();
      return await em.count(TransferListedPlayer, {
        auctionEndTime: { $gt: Date.now() },
        gameworldId,
      });
    } catch (error) {
      logger.error('Failed to count active auctions:', { error });
      throw error;
    }
  }
  /**
   * Get all transfer listed players for a gameworld
   * @param gameworldId The gameworld ID
   * @param limit Optional limit for pagination
   * @param lastEvaluatedKey Optional key for pagination
   * @returns Array of transfer listed players
   */
  async getTransferListedPlayers(
    gameworldId: string,
    limit?: number,
    lastEvaluatedKey?: string
  ): Promise<DBTransferListedPlayer[]> {
    const em = this.mikroOrmService.getEntityManager();
    const players = await em.find(
      TransferListedPlayer,
      { gameworldId, auctionEndTime: { $gt: Date.now() } },
      {
        limit,
        offset: lastEvaluatedKey ? Number.parseInt(lastEvaluatedKey) : undefined,
        populate: ['player', 'player.attributes', 'bidHistory', 'bidHistory.team'],
      }
    );
    return Promise.resolve(players);
  }

  /**
   * Get transfer listed players that a specific team has already bid on
   * @param teamId The ID of the team
   * @param gameworldId The gameworld ID
   * @returns Array of transfer listed players that the team has bid on
   */
  async getTransferListedPlayersWithTeamBids(
    teamId: string,
    gameworldId: string
  ): Promise<DBTransferListedPlayer[]> {
    try {
      const em = this.mikroOrmService.getEntityManager();

      // Find transfer listed players where the team has placed a bid
      const players = await em.find(
        TransferListedPlayer,
        {
          gameworldId,
          auctionEndTime: { $gt: Date.now() },
          bidHistory: { team: Reference.createFromPK(Team, teamId) },
        },
        {
          populate: ['player', 'player.attributes', 'bidHistory', 'bidHistory.team'],
        }
      );

      return Promise.resolve(players);
    } catch (error) {
      logger.error('Failed to get transfer listed players with team bids:', {
        error,
        teamId,
        gameworldId,
      });
      throw error;
    }
  }
}
