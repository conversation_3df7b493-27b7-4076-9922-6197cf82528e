import { submitOfferSchema } from '@/functions/transfers/schema.js';
import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { logger } from '@/utils/logger.js';

interface Body {
  player: string;
  offer: number;
  myTeam: string;
  theirTeam: string;
}

export const main = async function (event: HttpEvent<Body, void, void>) {
  logger.debug('Submitting transfer offer', { body: event.body });
  const { transferRepository } = event.context.repositories;

  const { player, offer, myTeam, theirTeam } = event.body;

  try {
    await transferRepository.submitOffer(player, offer, myTeam, theirTeam);
    return buildResponse(200, JSON.stringify({ message: 'Offer submitted successfully' }));
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
    if (error.message === 'DUPLICATE_TRANSFER_REQUEST') {
      return buildResponse(409, JSON.stringify({ message: 'Duplicate transfer request' }));
    }
    logger.error('Failed to submit transfer offer', { error });
    throw error;
  }
};

export const handler = httpMiddify(main, {
  schema: submitOfferSchema,
});
