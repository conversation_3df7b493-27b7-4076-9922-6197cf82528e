import { completePlayerTransfer } from '@/functions/transfers/transferUtils.js';
import { Repositories } from '@/middleware/database/types.js';
import { eventMiddify } from '@/middleware/event/index.js';
import { EventHandler } from '@/middleware/event/types.js';
import { NotificationManager } from '@/services/notifications/NotificationManager.js';
import { DBTransferRequest } from '@/storage-interface/transfers/index.js';
import { logger } from '@/utils/logger.js';

const notificationManager = NotificationManager.getInstance();

async function respondToTransferOffer(request: DBTransferRequest, repositories: Repositories) {
  try {
    // Unwrap references to get actual entities
    const player = request.player;
    const seller = request.seller;

    // Get all players from the seller's team
    const sellerPlayers = await seller.players.loadItems();

    // Sort players by value in descending order
    const sortedPlayers = [...sellerPlayers].sort((a, b) => b.value - a.value);

    // Find the player's rank in the team based on value
    const playerIndex = sortedPlayers.findIndex((p) => p.playerId === player.playerId);
    const totalPlayers = sortedPlayers.length;

    if (playerIndex === -1 || totalPlayers === 0) {
      // Player not found in the team or team has no players
      return;
    }

    // Calculate importance factor (0 to 1 scale, where 1 is most valuable)
    const importanceFactor = 1 - playerIndex / Math.max(1, totalPlayers - 1);

    // Calculate base multiplier based on importance
    const baseMultiplier = 0.8 + importanceFactor * (2.5 - 0.8);

    // Apply random +/- 50% variation to the multiplier
    // Random value between -0.5 and 0.5
    const randomFactor = Math.random() - 0.5;

    // Apply the random factor (this gives us +/- 50% of the base multiplier)
    const multiplier = baseMultiplier * (1 + randomFactor);

    // Calculate counter offer value
    const counterOfferValue = Math.round(player.value * multiplier);

    if (request.value >= counterOfferValue) {
      logger.debug('Counter offer is less than or equal to original offer. Deleting request', {
        requestId: request.id,
        originalOffer: request.value,
        counterOffer: counterOfferValue,
      });
      await completePlayerTransfer(
        request.player,
        request.buyer,
        request.seller,
        request.value,
        repositories
      );
      await repositories.transferRepository.deleteTransferRequest(request.id);
      return;
    }

    // Update the transfer request with the counter offer
    await repositories.transferRepository.submitCounterOffer(request, counterOfferValue, false);
    if (request.buyer.manager) {
      notificationManager.assignManagerPreferences(request.buyer.manager);
      await notificationManager.counterOfferMade(request, counterOfferValue);
    }

    return request;
  } catch (error) {
    logger.error('Failed to respond to transfer offer:', { error });
    throw error;
  }
}

export const main: EventHandler<void, void> = async (event) => {
  // Get repositories from context (injected by middleware)
  const { transferRepository } = event.context.repositories;

  const requests = await transferRepository.getTransferRequestsToAI();
  for (const request of requests) {
    await respondToTransferOffer(request, event.context.repositories);
  }
  await transferRepository.flush();
};

export const handler = eventMiddify(main);
