import { httpMiddify } from '@/middleware/rest/index.js';
import { HttpEvent } from '@/middleware/rest/types.js';
import {
  BidHistoryResponse,
  TransferListPathParameters as PathParameters,
  TransferListQueryParameters as QueryParameters,
  TransferListedPlayerResponse,
  TransferListResponse,
} from '@/model/transfer-list.js';
import { extractCurrentAttributes } from '@/utils/attributeUtils.js';
import { buildResponse } from '@/utils/buildResponse.js';
import { getUser } from '@/utils/getUser.js';
import { logger } from '@/utils/logger.js';

/**
 * Lambda function to get transfer listed players that the current user has bid on
 */
const main = async function (event: HttpEvent<void, PathParameters, QueryParameters>) {
  try {
    const { transferRepository, managerRepository } = event.context.repositories;

    // Get the current user ID
    const userId = getUser(event);
    if (!userId) {
      return buildResponse(401, JSON.stringify({ error: 'Unauthorized' }));
    }

    // Get the manager to find the team ID
    const manager = await managerRepository.getManagerById(userId);
    if (!manager || !manager.team) {
      return buildResponse(404, JSON.stringify({ error: 'Manager or team not found' }));
    }

    const teamId = manager.team.teamId;
    const gameworldId = event.pathParameters.gameworldId;

    // Query the transfer listed players that the team has bid on
    const result = await transferRepository.getTransferListedPlayersWithTeamBids(
      teamId,
      gameworldId
    );

    if (!result || result.length === 0) {
      return buildResponse(404, JSON.stringify({ error: 'No players found with your bids' }));
    }

    // Transform players to include only current attributes
    const transformedPlayers: TransferListedPlayerResponse[] = result.map((transferList) => {
      // Transform bid history to the response format
      const bidHistory: BidHistoryResponse[] = transferList.bidHistory.getItems().map((bid) => ({
        teamId: bid.team.teamId,
        teamName: bid.team.teamName,
        maximumBid: bid.maximumBid,
        bidTime: bid.bidTime,
      }));

      return {
        gameworldId: transferList.gameworldId,
        teamId: transferList.player.$.team?.teamId || '',
        leagueId: '',
        playerId: transferList.player.playerId,
        firstName: transferList.player.$.firstName,
        surname: transferList.player.$.surname,
        attributes: extractCurrentAttributes(transferList.player.$.attributes),
        age: transferList.player.$.age,
        value: transferList.player.$.value,
        auctionStartPrice: transferList.auctionStartPrice,
        auctionCurrentPrice: transferList.auctionCurrentPrice,
        auctionEndTime: transferList.auctionEndTime,
        bidHistory: bidHistory,
      };
    });

    const response: TransferListResponse = {
      players: transformedPlayers,
    };

    return buildResponse(200, JSON.stringify(response));
  } catch (error) {
    logger.error('Error getting transfer listed players with user bids:', { error });
    return buildResponse(500, JSON.stringify({ error: 'Internal server error' }));
  }
};

export const handler = httpMiddify(main, {});
